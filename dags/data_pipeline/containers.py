# coding=utf-8
import asyncio
import base64
import json
import logging.config
import os
import weakref
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from typing import (
    Dict, Any, Optional, Protocol, runtime_checkable, Self, AsyncGenerator, List, TypeVar, Generator
)

import aiohttp
import asyncpg
import pandas as pd
import yaml
from dependency_injector import containers, providers
from dependency_injector.wiring import inject, Provide
from psycopg2 import InterfaceError
from pykeepass import PyKeePass

from sqlalchemy import literal, case, cast, func, TEXT, create_engine, inspect
from sqlalchemy.engine import URL
from sqlalchemy.exc import DisconnectionError
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy_utils import LtreeType
import logging
from dags.data_pipeline.custom_logger import DatabaseLogHandlerFactory
from dags.data_pipeline.progress_manager import PostgresSessionManager
from dags.data_pipeline.utils.circuit_breaker import SimplifiedCircuitBreaker

from dags.data_pipeline.utils.shutdown_handler import ApplicationShutdownHandler

try:
    from dbmodels.issue import Issue
    from dbmodels.user import User
except ModuleNotFoundError:
    from .dbmodels.issue import Issue
    from .dbmodels.user import User

def _get_task_lifecycle_coordinator(logger):
    """Lazy import and create TaskLifecycleCoordinator to handle circular imports."""
    # Temporarily disabled to break circular import
    # try:
    #     from dags.data_pipeline.utility_code import TaskLifecycleCoordinator
    #     return TaskLifecycleCoordinator(logger)
    # except ImportError:
    #     # Handle circular import by returning None or a mock
    return None


# ============================================================================
# PROTOCOLS AND INTERFACES
# ============================================================================

# @runtime_checkable
# class DatabaseSessionProtocol(Protocol):
#     """
#     Protocol defining the interface for database session managers.
#
#     This protocol ensures consistent behavior across different session manager
#     implementations and helps PyCharm provide better type checking and autocompletion.
#     """
#
#     schema: str
#     closed: bool
#     logger: logging.Logger
#
#     def close(self) -> None:
#         """Synchronous cleanup method."""
#         ...
#
#     async def aclose(self) -> None:
#         """Asynchronous cleanup method."""
#         ...
#
#     def update_schema(self, new_schema: str) -> Self:
#         """Update the database schema."""
#         ...
#
#
# @runtime_checkable
# class SyncSessionProtocol(Protocol):
#     """Protocol for synchronous database sessions."""
#
#     @contextmanager
#     def session(self) -> Generator[Session, None, None]:
#         """Context manager for sync sessions."""
#         ...
#
#
# @runtime_checkable
# class AsyncSessionProtocol(Protocol):
#     """Protocol for asynchronous database sessions."""
#
#     @asynccontextmanager
#     async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
#         """Context manager for async sessions."""
#         ...
#
#
# @runtime_checkable
# class ConnectionRecoveryProtocol(Protocol):
#     """Protocol for connection recovery capabilities."""
#
#     MAX_RETRIES: int
#     RETRY_DELAY: float
#     engine_async: Optional[AsyncEngine]
#
#     async def _handle_connection_error(self, error: Exception, operation_name: str) -> bool:
#         """Handle connection errors and determine if retry is appropriate."""
#         ...
#
#     async def _recreate_async_engine(self) -> None:
#         """Recreate the async engine to establish new connections."""
#         ...
#
#     @asynccontextmanager
#     async def async_session_with_retry(self) -> AsyncGenerator[AsyncSession, None]:
#         """Context manager for async sessions with automatic retry."""
#         ...
#
#
# @runtime_checkable
# class SchemaManagementProtocol(Protocol):
#     """Protocol for schema management capabilities."""
#
#     def get_schema_history(self) -> List[str]:
#         """Get the history of schema changes."""
#         ...
#
#     def revert_schema(self) -> Self:
#         """Revert to the previous schema."""
#         ...
#
#
# # Type variables for better type hints
# T_SessionManager = TypeVar("T_SessionManager", bound=DatabaseSessionProtocol)
# T_ConnectionRecovery = TypeVar("T_ConnectionRecovery", bound=ConnectionRecoveryProtocol)

# ============================================================================
# ENTRY DETAILS AND CONFIGURATION
# ============================================================================


@dataclass
class EntryDetails:
    username: str
    password: str
    url: str | None = None
    custom_properties: Dict[str, Any] = field(default_factory=dict)

    def __repr__(self) -> str:
        return (f"EntryDetails(username={self.username!r}, password='****', "
                f"url={self.url!r}, custom_properties={self.custom_properties!r})")

    def __str__(self) -> str:
        return (f"EntryDetails:\n"
                f"  Username: {self.username}\n"
                f"  Password: ****\n"
                f"  URL: {self.url}\n"
                f"  Custom Properties: {self.custom_properties}")


class EntryDetailsBuilder:
    def __init__(self):
        # self._entry_details = EntryDetails(username='', password='')
        self._username: str | None = None
        self._password: str | None = None
        self._url: str | None = None
        self._custom_properties: Dict[str, Any] = {}

    def set_username(self, username: str) -> 'EntryDetailsBuilder':
        # self._entry_details.username = username
        self._username = username
        return self

    def set_password(self, password: str) -> 'EntryDetailsBuilder':
        # self._entry_details.password = password
        self._password = password
        return self

    def set_url(self, url: Optional[str]) -> 'EntryDetailsBuilder':
        # self._entry_details.url = url
        self._url = url
        return self

    def add_custom_property(self, key: str, value: Any) -> 'EntryDetailsBuilder':
        # self._entry_details.custom_properties[key] = value
        self._custom_properties[key] = value
        return self

    def set_rw_connection(self, host: str, port: int, db_name: str) -> 'EntryDetailsBuilder':
        self._custom_properties.update({
            "DB_SERVER_NAME": host,
            "DB_SERVER_RW_PORT": port,
            "DB_NAME": db_name,
        })
        return self

    def set_ro_connection(self, host: str, port: int, db_name: str) -> 'EntryDetailsBuilder':
        self._custom_properties.update({
            "DB_SERVER_NAME": host,
            "DB_SERVER_RO_PORT": port,
            "DB_NAME": db_name,
        })
        return self

    def build(self) -> EntryDetails:
        # return self._entry_details
        if not self._username or not self._password:
            raise ValueError("Username and password must be set before building EntryDetails")
        return EntryDetails(
            username=self._username,
            password=self._password,
            url=self._url,
            custom_properties=self._custom_properties
        )

def build_entry_details(keepass_manager: PyKeePass, title: str) -> EntryDetails:
    """Logic to retrieve and build EntryDetails, previously in get_kp_entry_details."""
    entry = keepass_manager.find_entries(title=title, first=True)
    if not entry:
        raise LookupError(f"No entry found with given title {title}")
    try:
        builder = (
            EntryDetailsBuilder()
            .set_username(entry.username or "")
            .set_password(entry.password or "")
            .set_url(entry.url)
        )

        # for custom_property in entry.custom_properties:
        #     builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))
        for key, value in (entry.custom_properties or {}).items():
            builder.add_custom_property(key, value)

        return builder.build()

    except ValueError as ve:
        raise RuntimeError(f"Invalid KeePass entry '{title}': {ve}") from ve
    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")


class LoggerContainer(containers.DeclarativeContainer):
    """
    Centralized logging container that provides configured loggers for the application.

    This container manages:
    - Logger configuration from YAML files
    - Multiple logger instances (main, profiled, debugging)
    - Proper resource management for logging setup
    - Consistent logger naming and configuration

    The container uses the Flowers refactoring pattern by:
    - Centralizing logger configuration
    - Providing reusable logger instances
    - Managing logging resources lifecycle
    - Enabling dependency injection of loggers

    Attributes:
        config_file: Configuration provider for logging settings
        log_resource: Resource provider for logging configuration
        logger: Main application logger (environment-specific)
        profiled_logger: Logger for performance profiling
        debug_logger: Logger for debugging operations

    Example:
        >>> logger_container = LoggerContainer()
        >>> logger_container.init_resources()
        >>> logger = logger_container.logger()
        >>> logger.info("Application started")
    """

    # Centralized configuration path management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config_file = providers.Configuration(yaml_files=[config_path])


    # Wiring configuration for dependency injection
    wiring_config = containers.WiringConfiguration(
        modules=[
            "__main__", __name__,
        ]
    )

    schema = providers.Object("public")  # Default schema
    # KeePass Manager
    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config_file.KeePassDir.DB_NAME,
        keyfile=config_file.KeePassDir.KEY_FILE
    )


    # Resource provider for logging configuration - properly managed
    log_resource = providers.Resource(
        logging.config.dictConfig,
        config=config_file.logging,
    )

    # Resource to install correlation factory
    correlation_factory_resource = providers.Resource(
        lambda: __import__('dags.data_pipeline.logging_utils', fromlist=['install_correlation_log_record_factory']).install_correlation_log_record_factory()
    )
    logging.getLogger().info("Factory installed test log")

    # Logger providers with proper dependency on log_resource and correlation factory
    logger = providers.Singleton(
        providers.Callable(
            lambda name, log_res, corr_res: logging.getLogger(name),
            name=config_file.Environment.Env,
            log_res=log_resource,
            corr_res=correlation_factory_resource
        )
    )

    profiled_logger = providers.Singleton(
        providers.Callable(
            lambda name, log_res, corr_res: logging.getLogger(name),
            name="profiled_logger",
            log_res=log_resource,
            corr_res=correlation_factory_resource
        )
    )

    # Additional specialized loggers
    debug_logger = providers.Singleton(
        providers.Callable(
            lambda name, log_res, corr_res: logging.getLogger(name),
            name="debug_utils",
            log_res=log_resource,
            corr_res=correlation_factory_resource
        )
    )

    debugging_monitor_logger = providers.Singleton(
        providers.Callable(
            lambda name, log_res, corr_res: logging.getLogger(name),
            name="debugging_monitor",
            log_res=log_resource,
            corr_res=correlation_factory_resource
        )
    )

    #
    # # Database logging handler factory (deferred initialization)
    database_log_handler = providers.Singleton(
        DatabaseLogHandlerFactory.create_deferred_handler,
        batch_size=50,
        flush_interval=30,
        schema=schema,
        enabled=True,
        level=logging.INFO
    )


# # ============================================================================
# # MIXINS FOR COMPOSABLE FUNCTIONALITY
# # ============================================================================
#
# class ConnectionRecoveryMixin:
#     """
#     Mixin to add connection recovery capabilities to PostgresSessionManager.
#
#     This mixin implements the Flowers refactoring pattern by:
#     - Separating connection recovery concerns from main session management
#     - Providing reusable recovery logic across different session managers
#     - Using dependency injection for logger access
#     - Centralizing retry and recovery configuration
#
#     Attributes:
#         MAX_RETRIES: Maximum number of retry attempts for connection recovery
#         RETRY_DELAY: Base delay between retry attempts (exponential backoff)
#         logger: Injected logger instance for recovery operations
#     """
#
#     MAX_RETRIES = 3
#     RETRY_DELAY = 1.0
#     engine_async: Optional[AsyncEngine]
#     _create_engine_async: MethodType
#
#     @inject
#     def __init__(self, logger: logging.Logger = Provide[LoggerContainer.logger], *args, **kwargs):
#         """
#         Initialize the connection recovery mixin.
#
#         Args:
#             logger: Injected logger instance from LoggerContainer
#             *args: Additional positional arguments for parent classes
#             **kwargs: Additional keyword arguments for parent classes
#         """
#         self.logger = logger
#         super().__init__(*args, **kwargs)
#
#     async def _handle_connection_error(self, error: Exception, operation_name: str = "operation") -> bool:
#         """
#         Handle connection errors and determine if retry is appropriate.
#
#         Args:
#             error: The exception that occurred
#             operation_name: Name of the operation for logging
#
#         Returns:
#             bool: True if the operation should be retried, False otherwise
#         """
#         # Check if this is a connection-related error
#         if isinstance(error, (
#                 InterfaceError,
#                 DisconnectionError,
#                 asyncpg.exceptions.InterfaceError,
#                 asyncpg.exceptions.ConnectionDoesNotExistError,
#                 asyncpg.exceptions.InvalidTransactionStateError,
#                 ConnectionError,
#         )):
#             self.logger.warning(f"Connection error during {operation_name}: {error}")
#             return True
#
#         # Check for specific SQLAlchemy error messages
#         error_msg = str(error).lower()
#         if any(msg in error_msg for msg in [
#             'connection is closed',
#             'connection was closed',
#             'connection terminated',
#             'server closed the connection',
#             'connection lost',
#             'connection broken'
#         ]):
#             self.logger.warning(f"Connection-related error during {operation_name}: {error}")
#             return True
#
#         return False
#
#     async def _recreate_async_engine(self):
#         """Recreate the async engine to establish new connections."""
#         self.logger.info("Recreating async database engine...")
#
#         if self.engine_async:
#             try:
#                 print(f"Disposing: {self.engine_async}")
#                 await self.engine_async.dispose()
#             except Exception as err:
#                 self.logger.warning(f"Error disposing old engine: {err}")
#
#         # Recreate the engine
#         self._create_engine_async()
#         self.logger.info("Async database engine recreated successfully")
#
# class PostgresSessionManager:
#     """
#     Manages PostgreSQL database connections with both synchronous and asynchronous support.
#
#     This class provides a comprehensive database session manager that handles:
#     - Both sync (psycopg2) and async (asyncpg) database connections
#     - Connection pooling and lifecycle management
#     - Schema switching capabilities
#     - Automatic cleanup and resource disposal
#     - Context managers for safe session handling
#
#     Attributes:
#         schema (str): Current database schema
#         entry (EntryDetails): Database connection details
#         rw (bool): Whether this is a read-write connection
#         engine: Synchronous SQLAlchemy engine
#         engine_async: Asynchronous SQLAlchemy engine
#
#     Example:
#         >>> entry = EntryDetails(username="user", password="pass", ...)
#         >>> manager = PostgresSessionManager(entry, "public", rw=True)
#         >>>
#         >>> # Synchronous usage
#         >>> with manager.session() as pg_session:
#         ...     result = pg_session.execute("SELECT 1")
#         >>>
#         >>> # Asynchronous usage
#         # >>> async with manager.async_session() as session:
#         # ...     result = await session.execute("SELECT 1")
#         >>>
#         >>> # Schema switching
#         >>> manager.update_schema("plat")
#         >>>
#         >>> # Cleanup
#         >>> manager.close()  # or await manager.aclose()
#     """
#     # Class-level registry to track all instances for cleanup
#     _instances = weakref.WeakSet()
#     logger: logging.Logger = None
#
#     @inject
#     def __init__(
#             self, entry: EntryDetails, schema: str, rw: bool = True,
#             logger: logging.Logger = Provide[LoggerContainer.logger]
#     ):
#         """
#         Initialize PostgresSessionManager with dependency injection.
#
#         This implementation follows the Flowers refactoring pattern by:
#         - Using dependency injection for logger access
#         - Centralizing configuration management
#         - Providing clean separation of concerns
#         - Enabling testability through injectable dependencies
#
#         Args:
#             entry: Database connection details from KeePass
#             schema: Database schema to use for operations
#             rw: Whether this is a read-write connection (default: True)
#             logger: Injected logger instance from LoggerContainer
#         """
#         self.schema = schema
#         self.entry = entry
#         self.rw = rw
#         self.engine_async = None  # Initialize with None
#         self.engine = None  # Initialize with None
#         self.closed = False
#         type(self).logger = logger  # Use instance-level logger instead of class-level
#
#         # Register this instance for cleanup
#         PostgresSessionManager._instances.add(self)
#
#         self._create_engine_async()
#         self._create_engine()
#
#     def _create_engine_async(self):
#         """Create asynchronous PostgreSQL engine with asyncpg driver."""
#         self.DATABASE_URL_ASYNC = URL.create(
#             drivername="postgresql+asyncpg",
#             username=self.entry.username,
#             password=self.entry.password,
#             host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
#             port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties[
#                 "DB_SERVER_RO_PORT"],
#             database=str(self.entry.custom_properties["DB_NAME"])
#         )
#         self.engine_async = create_async_engine(
#             self.DATABASE_URL_ASYNC,
#             echo=True,
#             pool_size=20,  # Adjust pool size as needed
#             max_overflow=30,  # Adjust max overflow as needed
#             pool_timeout=30,  # Timeout in seconds for acquiring a connection from the pool
#             pool_recycle=3600,  # Add connection recycling
#             pool_pre_ping=True,  # Add connection health checks
#             echo_pool=True,
#             logging_name=f"AsyncEngine_{self.schema}",
#             pool_logging_name=f"AsyncPool_{self.schema}",
#             isolation_level="READ COMMITTED",
#
#         ).execution_options(
#             schema_translate_map={None: self.schema}
#         )
#
#     def _create_engine(self):
#         """Create synchronous PostgreSQL engine with psycopg2 driver."""
#         # Ensure we're using psycopg2, not psycopg3
#         self.DATABASE_URL = URL.create(
#             drivername="postgresql+psycopg2",
#             username=self.entry.username,
#             password=self.entry.password,
#             host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
#             port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties[
#                 "DB_SERVER_RO_PORT"],
#             database=str(self.entry.custom_properties["DB_NAME"])
#         )
#
#         self.engine = create_engine(
#             self.DATABASE_URL,
#             pool_size=20,
#             max_overflow=10,
#             pool_recycle=3600,
#             pool_timeout=30,
#             pool_pre_ping=True,
#             echo=False,
#             echo_pool=False,
#             isolation_level="READ COMMITTED"
#         ).execution_options(
#             schema_translate_map={None: self.schema}
#         )
#
#     # ADD: Proper cleanup methods
#     def close(self):
#         """Synchronous cleanup method."""
#         if self.closed:
#             return
#         self.closed = True
#         try:
#             """Explicitly close all connections and dispose engines."""
#             if self.engine:
#                 self.engine.dispose()
#                 self.engine = None
#             # Handle async engine disposal properly
#             if self.engine_async:
#                 # Use run_until_complete for async engine disposal
#
#                 try:
#                     loop = asyncio.get_event_loop()
#                     # Schedule the disposal as a task
#                     loop.create_task(self._async_dispose())
#
#                     # if loop.is_running():
#                     #     # If we're in an async context, schedule disposal
#                     #     asyncio.create_task(self.engine_async.dispose())
#                     # else:
#                     #     loop.run_until_complete(self.engine_async.dispose())
#                 except RuntimeError:
#                     # No running loop, create a new one for cleanup
#                     try:
#                         asyncio.run(self._async_dispose())
#                     except Exception as err:
#                         self.logger.warning(f"Error during async engine disposal: {err}")
#                     # Create new event loop if none exists
#                     # new_loop = asyncio.new_event_loop()
#                     # new_loop.run_until_complete(self.engine_async.dispose())
#                     # new_loop.close()
#         except Exception as err:
#             self.logger.warning(f"Error during PostgresSessionManager cleanup: {err}")
#
#     async def _async_dispose(self):
#         """Helper method for async engine disposal."""
#         if self.engine_async:
#             try:
#                 await self.engine_async.dispose()
#                 self.engine_async = None
#             except asyncio.CancelledError:
#                 # Handle cancellation gracefully
#                 self.logger.info("Async engine disposal was cancelled")
#                 raise
#             except Exception as err:
#                 self.logger.warning(f"Error disposing async engine: {err}")
#                 raise
#
#     async def aclose(self):
#         """Async cleanup method."""
#         if self.closed:
#             return
#
#         self.closed = True
#         try:
#             if self.engine:
#                 self.engine.dispose()
#                 self.engine = None
#             if self.engine_async:
#                 await self.engine_async.dispose()
#                 self.engine_async = None
#         except asyncio.CancelledError:
#             self.logger.info("PostgresSessionManager async cleanup was cancelled")
#             raise
#         except Exception as err:
#             self.logger.warning(f"Error during PostgresSessionManager async cleanup: {err}")
#
#
#     def __del__(self):
#         """Ensure cleanup on garbage collection."""
#         if not self.closed:
#             try:
#                 # Only dispose sync engine in __del__ to avoid event loop issues
#                 if self.engine:
#                     self.engine.dispose()
#                 # self.engine = None
#             except Exception:
#                 pass
#
#     @contextmanager
#     def session(self) -> Generator[Session, None, None]:
#         """Context manager for sync sessions."""
#         if self.closed:
#             raise RuntimeError("PostgresSessionManager has been closed")
#
#         session_maker = sessionmaker(
#             bind=self.engine,
#             autocommit=False,
#             autoflush=False,
#         )
#         session = session_maker()
#         try:
#             yield session
#         except Exception:
#             session.rollback()
#             raise
#         finally:
#             session.close()
#
#     @asynccontextmanager
#     async def async_session(self) -> AsyncGenerator[Any, Any]:
#         if self.closed:
#             raise RuntimeError("PostgresSessionManager has been closed")
#         async_session = sessionmaker(
#             bind=self.engine_async,
#             class_=AsyncSession,
#             expire_on_commit=False,
#             autoflush=False,
#             autocommit=False,
#         )
#         async with async_session() as session:
#             try:
#                 yield session
#             except asyncio.CancelledError:
#                 await session.rollback()
#                 self.logger.info("Async session was cancelled, rolled back")
#                 raise
#             except Exception:
#                 await session.rollback()
#                 raise
#             finally:
#                 await session.close()
#
#     def update_schema(self, new_schema: str) -> Self:
#         """Dynamically updates the schema and refreshes the engines."""
#         if self.closed:
#             raise RuntimeError("PostgresSessionManager has been closed")
#
#         self.schema = new_schema
#
#         if self.engine_async:
#             # Update schema_translate_map on existing engines
#             self.engine_async = self.engine_async.execution_options(
#                 schema_translate_map={None: self.schema}
#             )
#         if self.engine:
#             self.engine = self.engine.execution_options(
#                 schema_translate_map={None: self.schema}
#             )
#
#         return self
#
#     def get_schemas(self) -> list[str]:
#         """
#         Retrieve all schemas from the regular (sync) engine.
#
#         :return: List of schema names.
#         """
#         if self.closed:
#             raise RuntimeError("PostgresSessionManager has been closed")
#         inspector = inspect(self.engine, raiseerr=True)
#         return inspector.get_schema_names()
#
#     async def get_schemas_async(self) -> list[str]:
#         """Retrieve all schemas from the async engine."""
#         if self.closed:
#             raise RuntimeError("PostgresSessionManager has been closed")
#
#         async with self.engine_async.connect() as connection:
#             return await connection.run_sync(
#                 lambda conn: inspect(conn).get_schema_names()
#             )
#
#     @classmethod
#     async def cleanup_all_instances(cls):
#         """Cleanup all active instances. Call this during application shutdown."""
#         cleanup_tasks = []
#         for instance in list(cls._instances):
#             if not instance.closed:
#                 cleanup_tasks.append(instance.aclose())
#
#         if cleanup_tasks:
#             try:
#                 await asyncio.gather(*cleanup_tasks, return_exceptions=True)
#             except Exception as err:
#                 cls.logger.warning(f"Error during cleanup of all instances: {err}")
#
# # ============================================================================
# # DATABASE LIFECYCLE MANAGEMENT
# # ============================================================================
#
# class DatabaseLifecycleManager:
#     """Manages the lifecycle of database connections for the application."""
#
#     @inject
#     def __init__(self, logger: logging.Logger = Provide[LoggerContainer.logger]):
#         self._session_managers = []
#         self._cleanup_registered = False
#         self._setup_cleanup_handlers()
#         self.logger = logger
#
#     def register_session_manager(self, session_manager: PostgresSessionManager):
#         """Register a session manager for cleanup."""
#         self._session_managers.append(session_manager)
#
#     def _setup_cleanup_handlers(self):
#         """Setup cleanup handlers for different shutdown scenarios."""
#         if self._cleanup_registered:
#             return
#
#         # Register atexit handler for normal program termination
#         atexit.register(self._sync_cleanup)
#
#         # Register signal handlers for graceful shutdown
#         signal.signal(signal.SIGTERM, self._signal_handler)
#         signal.signal(signal.SIGINT, self._signal_handler)
#
#         self._cleanup_registered = True
#
#     def _signal_handler(self, signum, frame):
#         """Handle shutdown signals."""
#         self.logger.info(f"Received signal {signum}, cleaning up database connections...{frame}")
#         self._sync_cleanup()
#
#     def _sync_cleanup(self):
#         """Synchronous cleanup method."""
#         for session_manager in self._session_managers:
#             try:
#                 session_manager.close()
#             except Exception as err:
#                 self.logger.warning(f"Error cleaning up session manager: {err}")
#
#         self._session_managers.clear()
#
#     async def async_cleanup(self):
#         """Asynchronous cleanup method."""
#         cleanup_tasks = []
#         for session_manager in self._session_managers:
#             if not session_manager.closed:
#                 cleanup_tasks.append(session_manager.aclose())
#
#         if cleanup_tasks:
#             try:
#                 await asyncio.gather(*cleanup_tasks, return_exceptions=True)
#             except Exception as err:
#                 self.logger.warning(f"Error during async cleanup: {err}")
#
#         self._session_managers.clear()
#
#
# # Global lifecycle manager
# db_lifecycle_manager = DatabaseLifecycleManager()
#
#
# class ManagedPostgresSessionManager(PostgresSessionManager):
#     """
#     PostgresSessionManager that auto-registers with the global lifecycle manager.
#
#     This class extends PostgresSessionManager to automatically register itself
#     with the global database lifecycle manager for automatic cleanup during
#     application shutdown. This ensures that database connections are properly
#     closed even if explicit cleanup is not called.
#
#     The lifecycle manager handles cleanup through:
#     - atexit handlers for normal program termination
#     - Signal handlers for SIGTERM and SIGINT
#     - Manual cleanup methods for controlled shutdown
#
#     Args:
#         entry (EntryDetails): Database connection details
#         schema (str): Database schema to use
#         rw (bool): Whether this is a read-write connection (default: True)
#
#     Example:
#         >>> entry = EntryDetails(username="user", password="pass", ...)
#         >>> manager = ManagedPostgresSessionManager(entry, "plat", rw=True)
#         >>> # Manager is automatically registered for cleanup
#         >>> with manager.session() as session:
#         ...     # Database operations
#         ...     pass
#         >>> # Cleanup will happen automatically on application shutdown
#     """
#
#     def __init__(self, entry: EntryDetails, schema: str, rw: bool = True, **kwargs):
#         super().__init__(entry=entry, schema=schema, rw=rw, **kwargs)
#         # Auto-register with lifecycle manager
#         db_lifecycle_manager.register_session_manager(self)
#
# # Updated container with managed session managers
# class EnhancedPostgresSessionManager(PostgresSessionManager, ConnectionRecoveryMixin):
#     """
#     Enhanced PostgresSessionManager with connection recovery capabilities.
#
#     This class extends the base PostgresSessionManager to handle connection
#     failures gracefully by implementing automatic retry logic and connection
#     recovery mechanisms.
#     """
#
#     @asynccontextmanager
#     async def async_session_with_retry(self) -> AsyncGenerator[AsyncSession, None]:
#         """
#         Context manager for async sessions with automatic retry on connection failures.
#
#         This method implements exponential backoff retry logic for connection
#         failures and automatically recreates the database engine when needed.
#
#         Yields:
#             AsyncSession: Database session with connection recovery
#
#         Raises:
#             Exception: If all retry attempts are exhausted
#         """
#         if self.closed:
#             raise RuntimeError("PostgresSessionManager has been closed")
#
#         last_error = None
#
#         for attempt in range(self.MAX_RETRIES):
#             try:
#                 async_session_factory = sessionmaker(
#                     bind=self.engine_async,
#                     class_=AsyncSession,
#                     expire_on_commit=False,
#                     autoflush=False,
#                     autocommit=False
#                 )
#
#                 async with async_session_factory() as session:
#                     try:
#                         yield session
#                         # If we get here, the operation was successful
#                         return
#                     except Exception as err:
#                         await session.rollback()
#
#                         # Check if this is a connection error we can retry
#                         if await self._handle_connection_error(err, f"session operation (attempt {attempt + 1})"):
#                             last_error = err
#
#                             # Don't retry on the last attempt
#                             if attempt < self.MAX_RETRIES - 1:
#                                 self.logger.info(f"Retrying in {self.RETRY_DELAY * (attempt + 1)} seconds...")
#                                 await asyncio.sleep(self.RETRY_DELAY * (attempt + 1))
#
#                                 # Recreate the engine for the next attempt
#                                 await self._recreate_async_engine()
#                                 continue
#
#                         # If it's not a connection error or we've exhausted retries, re-raise
#                         raise
#
#             except asyncio.CancelledError:
#                 self.logger.info("Async session was cancelled")
#                 raise
#             except Exception as err:
#                 # Check if this is a connection error at the session creation level
#                 if await self._handle_connection_error(err, f"session creation (attempt {attempt + 1})"):
#                     last_error = err
#
#                     if attempt < self.MAX_RETRIES - 1:
#                         self.logger.info(f"Retrying session creation in {self.RETRY_DELAY * (attempt + 1)} seconds...")
#                         await asyncio.sleep(self.RETRY_DELAY * (attempt + 1))
#                         await self._recreate_async_engine()
#                         continue
#
#                 # If it's not a connection error or final attempt, re-raise
#                 raise
#
#         # If we get here, all retries were exhausted
#         self.logger.error(f"All {self.MAX_RETRIES} retry attempts exhausted")
#         if last_error:
#             raise last_error
#         else:
#             raise RuntimeError("Session creation failed after all retry attempts")
#
#     @asynccontextmanager
#     async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
#         """
#         Enhanced async session context manager with connection recovery.
#
#         This replaces the original async_session method with retry logic.
#         """
#         async with self.async_session_with_retry() as session:
#             yield session
#
#
#
#
# # Context manager for proper async cleanup
# @asynccontextmanager
# async def database_lifecycle():
#     """Context manager for managing database lifecycle in async applications."""
#     try:
#         yield
#     finally:
#         await db_lifecycle_manager.async_cleanup()
#
#
# # Decorator for functions that need database cleanup
# def with_database_cleanup(func):
#     """Decorator that ensures database cleanup after function execution."""
#     if asyncio.iscoroutinefunction(func):
#         async def async_wrapper(*args, **kwargs):
#             try:
#                 return await func(*args, **kwargs)
#             finally:
#                 await db_lifecycle_manager.async_cleanup()
#         return async_wrapper
#     else:
#         def sync_wrapper(*args, **kwargs):
#             try:
#                 return func(*args, **kwargs)
#             finally:
#                 db_lifecycle_manager._sync_cleanup()
#         return sync_wrapper

# class DatabaseSessionManagerContainer(containers.DeclarativeContainer):
#     """
#     Dependency injection container for database session management.
#
#     This container implements the Flowers refactoring pattern by:
#     - Composing with LoggerContainer for centralized logging
#     - Providing reusable database session managers
#     - Managing database lifecycle through dependency injection
#     - Enabling configuration override for different environments
#
#     The container provides managed PostgreSQL session managers that automatically
#     register with the lifecycle manager for proper cleanup. It supports both
#     read-write and read-only database connections with configurable schemas.
#
#     Attributes:
#         logger_container: Composed LoggerContainer for logging services
#         config: Configuration provider loaded from YAML file
#         schema: Default database schema (defaults to "public")
#         pg_rw_entry: Provider for read-write database entry details
#         pg_ro_entry: Provider for read-only database entry details
#         database_rw: Singleton managed session manager for read-write operations
#         database_ro: Singleton managed session manager for read-only operations
#         lifecycle_manager: Global database lifecycle manager for cleanup
#
#     Example:
#         >>> container = DatabaseSessionManagerContainer()
#         >>> keepass_container = KeePassContainer()
#         >>> container.pg_rw_entry.override(keepass_container.pg_rw)
#         >>> container.pg_ro_entry.override(keepass_container.pg_ro)
#         >>> db_rw = container.database_rw()
#         >>> with db_rw.session() as session:
#         ...     # Perform database operations
#         ...     pass
#     """
#
#     # Container composition - include LoggerContainer
#     logger_container = providers.Container(LoggerContainer)
#
#     # Centralized configuration management
#     config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
#     config = providers.Configuration(yaml_files=[config_path])
#
#     schema = providers.Object("public")
#
#     # Improved wiring configuration
#     wiring_config = containers.WiringConfiguration(
#         modules=[
#             # "dags.data_pipeline.utility_code",
#             "__main__",
#             __name__,
#         ]
#     )
#
#     # Entry providers - to be overridden by KeePass container
#     pg_rw_entry = providers.Provider()
#     pg_ro_entry = providers.Provider()
#
#     # Use managed session managers with proper dependency injection
#     database_rw = providers.Factory(
#         PostgresSessionManager,  # Updated to use refactored class
#         entry=pg_rw_entry.provided,
#         schema=schema,
#         rw=True,
#         logger=logger_container.logger  # Inject logger from composed container
#     )
#
#     database_ro = providers.Factory(
#         PostgresSessionManager,  # Updated to use refactored class
#         entry=pg_ro_entry.provided,
#         schema=schema,
#         rw=False,
#         logger=logger_container.logger  # Inject logger from composed container
#     )
#
#     # Enhanced session managers with connection recovery
#     # database_rw_enhanced = providers.Singleton(
#     #     FullyEnhancedSessionManager,  # All capabilities: recovery + lifecycle + schema management
#     #     entry=pg_rw_entry.provided,
#     #     schema=schema,
#     #     rw=True,
#     #     logger=logger_container.logger
#     # )
#     #
#     # database_ro_enhanced = providers.Singleton(
#     #     FullyEnhancedSessionManager,  # All capabilities: recovery + lifecycle + schema management
#     #     entry=pg_ro_entry.provided,
#     #     schema=schema,
#     #     rw=False,
#     #     logger=logger_container.logger
#     # )
#
#     # Lifecycle manager provider
#     lifecycle_manager = providers.Object(db_lifecycle_manager)

# ============================================================================
# REFACTORED SESSION MANAGER ARCHITECTURE
# ============================================================================

@runtime_checkable
class SupportsAsyncSessionRecovery(Protocol):
    logger: logging.Logger
    engine_async: AsyncEngine | None
    schema: str
    closed: bool

    def _create_engine_async(self) -> None: ...
    def update_schema(self, new_schema: str) -> Self: ...
    async def async_session_with_retry(self) -> AsyncGenerator[AsyncSession, None]: ...

T = TypeVar("T", bound=SupportsAsyncSessionRecovery)
class BaseSessionManager(ABC):
    """
    Abstract base class for database session managers.

    This class defines the core interface that all session managers must implement,
    providing a consistent API for database operations across different implementations.
    """

    def __init__(self, entry: EntryDetails, schema: str, rw: bool = True, logger: Optional[logging.Logger] = None):
        """Initialize base session manager."""
        self.entry = entry
        self.schema = schema
        self.rw = rw
        self.logger = logger or logging.getLogger(__name__)
        self.closed = False

    @abstractmethod
    def close(self) -> None:
        """Synchronous cleanup method."""
        pass

    @abstractmethod
    async def aclose(self) -> None:
        """Asynchronous cleanup method."""
        pass

    @abstractmethod
    def update_schema(self, new_schema: str) -> Self:
        """Update the database schema."""
        pass


class AsyncSessionManager(BaseSessionManager):
    """
    Base class for asynchronous database session management.

    Provides core async session functionality with proper resource management
    and error handling patterns.
    """

    def __init__(self, entry: EntryDetails, schema: str, rw: bool = True, logger: Optional[logging.Logger] = None):
        """Initialize async session manager."""
        super().__init__(entry, schema, rw, logger)
        self.engine_async: Optional[AsyncEngine] = None
        self._create_engine_async()

    def _create_engine_async(self) -> None:
        """Create asynchronous PostgreSQL engine with asyncpg driver."""
        database_url = URL.create(
            drivername="postgresql+asyncpg",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties["DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )

        self.engine_async = create_async_engine(
            database_url,
            pool_size=20,
            max_overflow=10,
            pool_recycle=3600,
            pool_timeout=30,
            pool_pre_ping=True,
            echo=False,
            echo_pool=False,
            isolation_level="READ COMMITTED"
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Create async session context manager."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        async_session_factory = sessionmaker(
            bind=self.engine_async,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False,
        )

        async with async_session_factory() as session:
            try:
                yield session
            except asyncio.CancelledError:
                await session.rollback()
                self.logger.info("Async session was cancelled, rolled back")
                raise
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    async def aclose(self) -> None:
        """Async cleanup method."""
        if self.closed:
            return

        self.closed = True
        try:
            if self.engine_async:
                await self.engine_async.dispose()
                self.engine_async = None
        except asyncio.CancelledError:
            self.logger.info("AsyncSessionManager cleanup was cancelled")
            raise
        except Exception as err:
            self.logger.warning(f"Error during AsyncSessionManager cleanup: {err}")

    def close(self) -> None:
        """Synchronous cleanup - delegates to async cleanup."""
        if self.closed:
            return

        try:
            asyncio.run(self.aclose())
        except RuntimeError:
            # No running loop, create a new one for cleanup
            try:
                loop = asyncio.new_event_loop()
                loop.run_until_complete(self.aclose())
                loop.close()
            except Exception as err:
                self.logger.warning(f"Error during sync cleanup: {err}")

    def update_schema(self, new_schema: str) -> Self:
        """Update schema and refresh engine options."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        self.schema = new_schema
        if self.engine_async:
            self.engine_async = self.engine_async.execution_options(
                schema_translate_map={None: self.schema}
            )
        return self
#
#
# ============================================================================
# MIXINS FOR COMPOSABLE FUNCTIONALITY
# ============================================================================

class ConnectionRecoveryMixin:
    """
    Mixin providing connection recovery capabilities with retry logic.

    This mixin can be composed with any session manager to add automatic
    retry and recovery functionality for connection failures.
    """

    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 1.0

    def __init__(self, *args, **kwargs):
        """Initialize connection recovery mixin."""
        super().__init__(*args, **kwargs)


    async def _handle_connection_error(self, error: Exception, operation_name: str = "operation") -> bool:
        """
        Handle connection errors and determine if retry is appropriate.

        Args:
            error: The exception that occurred
            operation_name: Name of the operation for logging

        Returns:
            bool: True if the operation should be retried, False otherwise
        """
        # Check if this is a connection-related error
        # self_typed = typing_cast(SupportsAsyncSessionRecovery, self)
        if isinstance(error, (
            InterfaceError,
            DisconnectionError,
            asyncpg.exceptions.InterfaceError,
            asyncpg.exceptions.ConnectionDoesNotExistError,
            asyncpg.exceptions.InvalidTransactionStateError,
            ConnectionError,
        )):
            # self_typed.logger.warning(f"Connection error during {operation_name}: {error}")
            return True
        return False

    async def _recreate_async_engine(self):
        """Recreate the async engine to establish new connections."""
        self.logger.info("Recreating async database engine...")

        if hasattr(self, 'engine_async') and self.engine_async:
            try:
                await self.engine_async.dispose()
            except Exception as err:
                self.logger.warning(f"Error disposing old engine: {err}")

        # Recreate the engine
        self._create_engine_async()
        self.logger.info("Async database engine recreated successfully")

    @asynccontextmanager
    async def async_session_with_retry(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Context manager for async sessions with automatic retry on connection failures.

        Implements exponential backoff retry logic for connection failures
        and automatically recreates the database engine when needed.
        """
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        last_error = None

        for attempt in range(self.MAX_RETRIES):
            try:
                # Call AsyncSessionManager.async_session directly to avoid recursion
                async with AsyncSessionManager.async_session(self) as session:
                    yield session
                return  # Success, exit retry loop

            except Exception as error:
                last_error = error

                # Check if this error warrants a retry
                if await self._handle_connection_error(error, f"session_creation_attempt_{attempt + 1}"):
                    if attempt < self.MAX_RETRIES - 1:  # Don't sleep on last attempt
                        delay = self.RETRY_DELAY * (2 ** attempt)  # Exponential backoff
                        self.logger.info(f"Retrying in {delay} seconds (attempt {attempt + 1}/{self.MAX_RETRIES})")
                        await asyncio.sleep(delay)

                        # Recreate engine for next attempt
                        await self._recreate_async_engine()
                        continue

                # Non-recoverable error or max retries reached
                self.logger.error(f"Non-recoverable error or max retries reached: {error}")
                raise

        # If we get here, all retries were exhausted
        self.logger.error(f"All {self.MAX_RETRIES} retry attempts exhausted")
        if last_error:
            raise last_error
        else:
            raise RuntimeError("Session creation failed after all retry attempts")


class LifecycleManagementMixin:
    """
    Mixin providing automatic lifecycle management and cleanup registration.

    This mixin automatically registers session managers with a global lifecycle
    manager for proper cleanup during application shutdown.
    """

    def __init__(self, *args, **kwargs):
        """Initialize lifecycle management mixin."""
        super().__init__(*args, **kwargs)
        # Auto-register with lifecycle manager
        if hasattr(self, '_register_for_cleanup'):
            self._register_for_cleanup()

    def _register_for_cleanup(self):
        """Register this instance for automatic cleanup."""
        # This will be implemented by concrete classes that need lifecycle management
        pass


class SchemaManagementMixin:
    """
    Mixin providing enhanced schema management capabilities.

    This mixin adds advanced schema switching and validation functionality.
    """

    def __init__(self, *args, **kwargs):
        """Initialize schema management mixin."""
        super().__init__(*args, **kwargs)
        # Initialize schema history if not already present
        if not hasattr(self, '_schema_history'):
            self._schema_history: List[str] = [getattr(self, 'schema', 'public')]

    def update_schema(self, new_schema: str) -> Self:
        """Update schema with history tracking."""
        if getattr(self, 'closed', False):
            raise RuntimeError("SessionManager has been closed")

        old_schema = getattr(self, 'schema', 'public')
        result = super().update_schema(new_schema)

        # Track schema changes
        if new_schema != old_schema:
            self._schema_history.append(new_schema)
            self.logger.debug(f"Schema updated from '{old_schema}' to '{new_schema}'")

        return result

    def get_schema_history(self) -> List[str]:
        """Get the history of schema changes."""
        return self._schema_history.copy()

    def revert_schema(self) -> Self:
        """Revert to the previous schema."""
        if len(self._schema_history) > 1:
            self._schema_history.pop()  # Remove current schema
            previous_schema = self._schema_history[-1]
            return self.update_schema(previous_schema)
        else:
            self.logger.warning("No previous schema to revert to")
            return self


# # ============================================================================
# # REFACTORED CONCRETE SESSION MANAGER CLASSES
# # ============================================================================
#
class RefactoredPostgresSessionManager(SchemaManagementMixin, AsyncSessionManager):
    """
    Refactored PostgreSQL session manager with both sync and async support.

    This class provides a comprehensive database session manager that handles:
    - Both sync (psycopg2) and async (asyncpg) database connections
    - Connection pooling and lifecycle management
    - Schema switching capabilities
    - Automatic cleanup and resource disposal
    - Context managers for safe session handling

    This implementation consolidates the functionality that was previously
    spread across multiple manager classes, eliminating the need for
    separate EnhancedSessionManager and ManagedSessionManager classes.
    """

    # Class-level registry to track all instances for cleanup
    _instances = weakref.WeakSet()

    def __init__(
            self,
            entry: EntryDetails,
            schema: str,
            rw: bool = True,
            logger: Optional[logging.Logger] = None,
            create_async: bool = True,
            create_sync: bool = True
    ):
        """
        Initialize refactored session manager.
        Args:
            entry: Database connection details
            schema: Database schema to use for operations
            rw: Whether this is a read-write connection (default: True)
            logger: Logger instance for logging operations
        """
        self.schema = schema
        self.entry = entry
        self.rw = rw
        self.logger = logger or logging.getLogger(__name__)
        self.closed = False
        self.async_closed = False

        # Initialize engines
        self.engine = None
        self.engine_async = None

        super().__init__(entry, schema, rw, logger)
        # super().__init__()
        self._create_engine()
        if create_async:
            self._create_engine_async()

        # Register this instance for cleanup
        RefactoredPostgresSessionManager._instances.add(self)


    def _create_engine(self) -> None:
        """Create synchronous PostgreSQL engine with psycopg2 driver."""
        database_url = URL.create(
            drivername="postgresql+psycopg2",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties["DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )

        self.engine = create_engine(
            database_url,
            pool_size=20,
            max_overflow=10,
            pool_recycle=3600,
            pool_timeout=60,
            pool_pre_ping=True,
            echo=False,
            echo_pool=False,
            isolation_level="READ COMMITTED",
            connect_args={
                "connect_timeout": "30",
                "keepalives": "1",
                "keepalives_idle": "30",
                "keepalives_interval": "10",
                "keepalives_count": "5",
            }
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    def _create_engine_async(self) -> None:
        """Create asynchronous PostgreSQL engine with asyncpg driver."""
        database_url = URL.create(
            drivername="postgresql+asyncpg",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw
            else self.entry.custom_properties["DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )

        self.engine_async = create_async_engine(
            database_url,
            pool_size=20,
            max_overflow=10,
            pool_recycle=3600,
            pool_timeout=120,
            pool_pre_ping=True,
            echo=False,
            echo_pool=False,
            isolation_level="READ COMMITTED",
            connect_args={
                "timeout": 240,  # asyncpg-specific, for connection timeout
                "statement_cache_size": 1000,
            }
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        """Context manager for sync sessions."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        session_maker = sessionmaker(
            bind=self.engine,
            autocommit=False,
            autoflush=False,
        )
        session = session_maker()
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Context manager for async sessions."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        async_session_factory = sessionmaker(
            bind=self.engine_async,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False,
        )

        async with async_session_factory() as session:
            try:
                yield session
            except asyncio.CancelledError:
                await session.rollback()
                self.logger.info("Async session was cancelled, rolled back")
                raise
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    @asynccontextmanager
    async def _base_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Base async session method for recovery mixin."""
        async with self.async_session() as session:
            yield session



    def update_schema(self, new_schema: str) -> Self:
        """Update schema for both sync and async engines."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        # Update async engine via parent
        super().update_schema(new_schema)

        # Update sync engine
        if self.engine:
            self.engine = self.engine.execution_options(
                schema_translate_map={None: self.schema}
            )

        if self.engine_async:
            self.engine_async = self.engine_async.execution_options(
                schema_translate_map={None: self.schema}
            )

        return self

    def get_schemas(self) -> List[str]:
        """Retrieve all schemas from the sync engine."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        inspector = inspect(self.engine)
        return inspector.get_schema_names()

    async def get_schemas_async(self) -> List[str]:
        """Retrieve all schemas from the async engine."""
        if self.closed:
            raise RuntimeError("SessionManager has been closed")

        async with self.engine_async.connect() as connection:
            return await connection.run_sync(
                lambda conn: inspect(conn).get_schema_names()
            )

    def close(self) -> None:
        """Synchronous cleanup method."""
        self.logger.info(f"RefactoredPostgresSessionManager close called for {self.entry.custom_properties['DB_NAME']}")
        self.logger.debug(f"self.closed = {self.closed}")
        if self.closed:
            return

        self.closed = True
        try:
            if self.engine:
                self.engine.dispose()
                self.engine = None

            # Handle async engine disposal
            # if self.engine_async:
            #     try:
            #         loop = asyncio.get_event_loop()
            #         if loop.is_running():
            #             # Schedule disposal as a task
            #             loop.create_task(self._async_dispose())
            #         else:
            #             loop.run_until_complete(self._async_dispose())
            #     except RuntimeError:
            #         # No running loop, create a new one for cleanup
            #         try:
            #             asyncio.run(self._async_dispose())
            #         except Exception as err:
            #             self.logger.warning(f"Error during async engine disposal: {err}")
        except Exception as err:
            self.logger.warning(f"Error during SessionManager cleanup: {err}")

    async def _async_dispose(self):
        """Helper method for async engine disposal."""
        self.logger.info("RefactoredPostgresSessionManager _async_dispose called")
        if self.engine_async:
            try:
                await self.engine_async.dispose()
                self.engine_async = None
            except asyncio.CancelledError:
                self.logger.info("Async engine disposal was cancelled")
                raise
            except Exception as err:
                self.logger.warning(f"Error disposing async engine: {err}")
                raise

    async def aclose(self) -> None:
        """Enhanced async cleanup method."""
        self.logger.info("RefactoredPostgresSessionManager aclose called")
        self.logger.debug(f"self.closed = {self.closed}")

        if self.async_closed:
            return

        self.async_closed = True
        try:
            # if self.engine:
            #     self.engine.dispose()
            #     self.engine = None
            if self.engine_async:
                self.logger.info("Disposing async engine in proper async context")
                await self.engine_async.dispose()
                self.engine_async = None
        except Exception as err:
            self.logger.warning(f"Error during RefactoredPostgresSessionManager cleanup: {err}")

    def __del__(self):
        """Ensure cleanup on garbage collection."""
        import sys
        import traceback

        if self.engine_async:
            # CRITICAL: Cannot dispose async engine in __del__
            self.logger.error("ASYNC ENGINE NOT PROPERLY CLOSED! This will cause MissingGreenlet errors!")
            self.logger.error("Always call aclose() before object destruction")

        if self.engine and not self.closed:
            try:
                # Only dispose sync engine in __del__ to avoid event loop issues
                if self.engine:
                    self.engine.dispose()
            except Exception:
                exc_type, exc_value, exc_tb = sys.exc_info()
                line_num = exc_tb.tb_lineno
                tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
                self.logger.error(
                    f"Line {line_num} Error encountered in __del__: {''.join(tb.format_exception_only())}",
                    exc_info=True
                )
                pass
        self.logger.info("RefactoredPostgresSessionManager __del__ exiting!!!")

    @classmethod
    async def cleanup_all_instances(cls):
        """Cleanup all active instances. Call this during application shutdown."""
        # cleanup_tasks = []
        # for instance in list(cls._instances):
        #     if not instance.closed:
        #         cleanup_tasks.append(instance.aclose())
        #
        # if cleanup_tasks:
        #     try:
        #         await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        #     except Exception as err:
        #         logging.getLogger(__name__).warning(f"Error during cleanup of all instances: {err}")
        instances = list(cls._instances)
        print(f"Cleaning up {len(instances)} PostgresSessionManager instances")
        cls._instances.clear()

        # async def cleanup_instance(instance):
        #     try:
        #         if hasattr(instance, 'engine_async') and instance.engine_async and not instance.async_closed:
        #             await instance.aclose()
        #         if not instance.closed:
        #             instance.close()
        #     except Exception as e:
        #         print(f"Error cleaning up instance {instance}: {e}")
        #
        # await asyncio.gather(*(cleanup_instance(instance) for instance in instances), return_exceptions=True)
        for instance in instances:
            # Close sync engine if open
            if instance.engine and not instance.closed:
                try:
                    instance.close()
                except Exception as e:
                    instance.logger.error(f"Error closing sync engine: {e}")

            # Close async engine if present & open
            if getattr(instance, "engine_async", None) and not instance.async_closed:
                try:
                    await instance.aclose()
                except Exception as e:
                    instance.logger.error(f"Error closing async engine: {e}")

#
# class EnhancedSessionManager(RefactoredPostgresSessionManager, ConnectionRecoveryMixin):
#     """
#     Enhanced session manager with connection recovery capabilities.
#
#     This class combines the refactored session manager with connection recovery
#     functionality, providing automatic retry logic for connection failures.
#     """
#
#     def __init__(
#             self,
#             entry: EntryDetails,
#             schema: str,
#             rw: bool = True,
#             logger: Optional[logging.Logger] = None,
#             max_retries: int = 3,
#             retry_delay: float = 1.0
#     ):
#         """Initialize enhanced session manager."""
#         # Set retry configuration before calling super().__init__()
#         self.MAX_RETRIES = max_retries
#         self.RETRY_DELAY = retry_delay
#         super().__init__(entry, schema, rw, logger)
#
#     @asynccontextmanager
#     async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
#         """
#         Enhanced async session context manager with connection recovery.
#
#         This replaces the original async_session method with retry logic.
#         """
#         async with self.async_session_with_retry() as session:
#             yield session


# class ManagedSessionManager(LifecycleManagementMixin, RefactoredPostgresSessionManager):
#     """
#     Managed session manager with automatic lifecycle management.
#
#     This class extends the refactored session manager to automatically register
#     itself with the global database lifecycle manager for automatic cleanup.
#
#     Note: LifecycleManagementMixin is listed first to ensure its __init__ is called.
#     """
#
#     def __init__(self, entry: EntryDetails, schema: str, rw: bool = True, logger: Optional[logging.Logger] = None):
#         """Initialize managed session manager."""
#         super().__init__(entry, schema, rw, logger)

    # def _register_for_cleanup(self):
    #     """Register this instance for automatic cleanup."""
    #     # Auto-register with lifecycle manager
    #     db_lifecycle_manager.register_session_manager(self)


# class FullyEnhancedSessionManager(
#     LifecycleManagementMixin, ConnectionRecoveryMixin,
#     RefactoredPostgresSessionManager):
#     """
#     Fully enhanced session manager with all capabilities.
#
#     This class combines all available mixins to provide:
#     - Connection recovery with retry logic
#     - Automatic lifecycle management
#     - Enhanced schema management
#     - Both sync and async session support
#
#     Note: LifecycleManagementMixin is listed first to ensure its __init__ is called.
#     """
#
#     def __init__(self, entry: EntryDetails, schema: str, rw: bool = True, logger: Optional[logging.Logger] = None):
#         """Initialize fully enhanced session manager."""
#         super().__init__(entry, schema, rw, logger)
#
#     def _register_for_cleanup(self):
#         """Register this instance for automatic cleanup."""
#         # Auto-register with lifecycle manager
#         db_lifecycle_manager.register_session_manager(self)
#
#     @asynccontextmanager
#     async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
#         """
#         Fully enhanced async session with recovery and lifecycle management.
#         """
#         async with self.async_session_with_retry() as session:
#             yield session




# ============================================================================
# ENHANCED DEPENDENCY INJECTION CONTAINERS
# ============================================================================

# class CoreSessionManagerContainer(containers.DeclarativeContainer):
#     """
#     Core container for session manager dependencies.
#
#     This container provides the fundamental session manager components
#     with clear separation of concerns and easy test overrides.
#     """
#
#     # Configuration
#     schema = providers.Configuration()
#
#     # Logger composition
#     # logger_container = providers.DependenciesContainer()
#
#     logger_container = providers.Container(LoggerContainer)
#
#
#     # Entry providers - to be overridden by parent containers
#     pg_rw_entry = providers.Provider()
#     pg_ro_entry = providers.Provider()
#
#     # Core session managers with factory pattern for easy testing
#     base_session_manager_rw = providers.Factory(
#         RefactoredPostgresSessionManager,
#         entry=pg_rw_entry.provided,
#         schema=schema,
#         rw=True,
#         logger=logger_container.logger
#     )
#
#     base_session_manager_ro = providers.Factory(
#         RefactoredPostgresSessionManager,
#         entry=pg_ro_entry.provided,
#         schema=schema,
#         rw=False,
#         logger=logger_container.logger
#     )
#
#     # Enhanced session managers with connection recovery
#     enhanced_session_manager_rw = providers.Factory(
#         EnhancedSessionManager,
#         entry=pg_rw_entry.provided,
#         schema=schema,
#         rw=True,
#         logger=logger_container.logger
#     )
#
#     enhanced_session_manager_ro = providers.Factory(
#         EnhancedSessionManager,
#         entry=pg_ro_entry.provided,
#         schema=schema,
#         rw=False,
#         logger=logger_container.logger
#     )
#
#     # Managed session managers with lifecycle management
#     # managed_session_manager_rw = providers.Singleton(
#     #     ManagedSessionManager,
#     #     entry=pg_rw_entry.provided,
#     #     schema=schema,
#     #     rw=True,
#     #     logger=logger_container.logger
#     # )
#     #
#     # managed_session_manager_ro = providers.Singleton(
#     #     ManagedSessionManager,
#     #     entry=pg_ro_entry.provided,
#     #     schema=schema,
#     #     rw=False,
#     #     logger=logger_container.logger
#     # )
#
#     # Fully enhanced session managers with all capabilities
#     # fully_enhanced_session_manager_rw = providers.Factory(
#     #     FullyEnhancedSessionManager,
#     #     entry=pg_rw_entry.provided,
#     #     schema=schema,
#     #     rw=True,
#     #     logger=logger_container.logger
#     # )
#     #
#     # fully_enhanced_session_manager_ro = providers.Factory(
#     #     FullyEnhancedSessionManager,
#     #     entry=pg_ro_entry.provided,
#     #     schema=schema,
#     #     rw=False,
#     #     logger=logger_container.logger
#     # )

class DatabaseContainer(containers.DeclarativeContainer):
    # Configuration - resolve path relative to this file's location
    _config_path = os.path.join(os.path.dirname(__file__), "config.yaml")
    config = providers.Configuration(yaml_files=[_config_path])

    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE,
    )

    # Create individual session managers for each schema
    # This avoids the scoping issue with dictionary comprehensions
    plat_session_manager = providers.Singleton(
        PostgresSessionManager.create_sync_only,
        entry=providers.Factory(
            build_entry_details,
            keepass_manager=keepass_manager,
            title="plat_rw"
        ),
        schema="plat",
        rw=True,
        logger=providers.Factory(logging.getLogger, "db.sync.plat")
    )



    # Now create the selector using the individual managers
    # sync_session_managers = providers.Selector(
    #     config.schema_name,
    #     plat=plat_session_manager,
    #     plp=plp_session_manager,
    #     public=public_session_manager
    # )
    sync_session_managers_ro = providers.Dict(
        public=providers.Singleton(
            PostgresSessionManager.create_sync_only,
            entry=providers.Factory(
                build_entry_details,
                keepass_manager=keepass_manager,
                title=f"public_ro"
            ),
            schema="public",
            rw=False,
            logger=providers.Factory(logging.getLogger, f"db.sync.public")
        )
    )
    sync_session_managers = providers.Dict(
        ccp=providers.Singleton(
            PostgresSessionManager.create_sync_only,
            entry=providers.Factory(
                build_entry_details,
                keepass_manager=keepass_manager,
                title=f"ccp_rw"
            ),
            schema="ccp",
            rw=True,
            logger=providers.Factory(logging.getLogger, f"db.sync.plat")
        ),
        plat=providers.Singleton(
                PostgresSessionManager.create_sync_only,
                entry=providers.Factory(
                    build_entry_details,
                    keepass_manager=keepass_manager,
                    title=f"plat_rw"
                ),
                schema="plat",
                rw=True,
                logger=providers.Factory(logging.getLogger, f"db.sync.plat")
        ),
        public=providers.Singleton(
            PostgresSessionManager.create_sync_only,
            entry=providers.Factory(
                build_entry_details,
                keepass_manager=keepass_manager,
                title=f"public_rw"
            ),
            schema="public",
            rw=True,
            logger=providers.Factory(logging.getLogger, f"db.sync.public")
        )
    )

    async_session_managers = providers.Dict(
        ccp=providers.Singleton(
            PostgresSessionManager.create_async_only,
            entry=providers.Factory(
                build_entry_details,
                keepass_manager=keepass_manager,
                title=f"ccp_rw"
            ),
            schema="ccp",
            rw=True,
            logger=providers.Factory(logging.getLogger, f"db.sync.ccp")
        ),
        plat=providers.Singleton(
            PostgresSessionManager.create_async_only,
            entry=providers.Factory(
                build_entry_details,
                keepass_manager=keepass_manager,
                title=f"plat_rw"
            ),
            schema="plat",
            rw=True,
            logger=providers.Factory(logging.getLogger, f"db.sync.plat")
        ),
        public=providers.Singleton(
            PostgresSessionManager.create_async_only,
            entry=providers.Factory(
                build_entry_details,
                keepass_manager=keepass_manager,
                title=f"public_rw"
            ),
            schema="public",
            rw=True,
            logger=providers.Factory(logging.getLogger, f"db.sync.public")
        )
    )

    # sync_session_managers = providers.Dict(
    #     **{
    #         schema: providers.Singleton(
    #             PostgresSessionManager.create_sync_only,
    #             entry=providers.Factory(
    #                 build_entry_details,
    #                 keepass_manager=providers.Callable(
    #                     PyKeePass,
    #                     filename="c:/KeePass/Database.kdbx",
    #                     keyfile="c:/KeePass/Database.key"
    #                 ),
    #                 title=f"{schema}_rw"
    #             ),
    #             schema=schema,
    #             rw=True,
    #             logger=providers.Factory(logging.getLogger, f"db.sync.{schema}")
    #         )
    #         for schema in config.schemas.provided  # Will be resolved from config
    #     }
    # )

    # async_session_managers = providers.Dict(
    #     **{
    #         schema: providers.Singleton(
    #             PostgresSessionManager.create_async_only,
    #             entry=providers.Factory(
    #                 build_entry_details,
    #                 keepass_manager=keepass_manager,
    #                 title=f"{schema}_rw"
    #             ),
    #             schema=schema,
    #             rw=True,
    #             logger=providers.Factory(logging.getLogger, f"db.sync.{schema}")
    #         )
    #         for schema in config.schemas.provided  # Will be resolved from config
    #     }
    # )
    #
    # dual_session_managers  = providers.Dict(
    #     **{
    #         schema: providers.Singleton(
    #             PostgresSessionManager.create_dual,
    #             entry=providers.Factory(
    #                 build_entry_details,
    #                 keepass_manager=keepass_manager,
    #                 title=f"{schema}_rw"
    #             ),
    #             schema=schema,
    #             rw=True,
    #             logger=providers.Factory(logging.getLogger, f"db.sync.{schema}")
    #         )
    #         for schema in config.schemas.provided  # Will be resolved from config
    #     }
    # )




class TaskCoordinationContainer(containers.DeclarativeContainer):
    """
    Container for task coordination and lifecycle management components.

    This container provides task lifecycle coordination, shutdown handling,
    and monitoring components with proper dependency injection.
    """

    # Logger composition
    logger_container = providers.DependenciesContainer()

    # Task lifecycle coordinator (with lazy import to handle circular dependencies)
    task_lifecycle_coordinator = providers.Callable(
        lambda logger: _get_task_lifecycle_coordinator(logger),
        logger=logger_container.logger
    )

    # Enhanced shutdown handler
    shutdown_handler = providers.Singleton(
        ApplicationShutdownHandler
    )




class FieldNameExtractor:
    def __init__(self, config):
        self.config = config

    def get_field_names(self):
        return [field_name["id"] for field_name in self.config["fields"]]

    def get_field_ids_by_datatype(self, datatype):
        """
        Given a datatype, returns the list of 'id' values for fields with the specified datatype.

        :param datatype: The datatype to filter by
        :return: List of 'id' values matching the datatype
        """
        return [field['id'] for field in self.config["fields"] if field['datatype'] == datatype]


class IssueFieldsContainer(containers.DeclarativeContainer):
    """
    Container for JIRA issue field configuration management.

    This container implements the Flowers refactoring pattern by:
    - Centralizing issue field configuration
    - Providing reusable field name extraction
    - Separating field configuration concerns
    - Enabling configuration override for different environments

    Attributes:
        config: Configuration provider for issue fields
        field_name_extractor: Factory for field name extraction utilities
    """

    # Specialized configuration for issue fields
    config_path = os.getenv("ISSUE_FIELDS_YAML_FILE", "enhanced_issue_fields.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # Field name extractor with proper configuration injection
    field_name_extractor = providers.Factory(
        FieldNameExtractor,
        config=config,
    )

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration(
        modules=[
            "__main__",
            __name__,
            # "dags.data_pipeline.utility_code",
        ]
    )


class KeePassContainer(containers.DeclarativeContainer):
    """
    Container for KeePass credential management.

    This container implements the Flowers refactoring pattern by:
    - Centralizing credential management configuration
    - Providing reusable entry detail builders
    - Using consistent configuration management
    - Enabling environment-specific credential access

    Attributes:
        config: Configuration provider for KeePass settings
        keepass_manager: Singleton KeePass manager instance
        jira_entry_details: Factory for JIRA credential details
        pg_rw: Factory for PostgreSQL read-write credentials
        pg_ro: Factory for PostgreSQL read-only credentials
        schema_rw: Factory for schema-specific read-write credentials
        schema_ro: Factory for schema-specific read-only credentials
    """

    # Centralized configuration management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration(
        modules=[
            # "dags.data_pipeline.containers",
            # "dags.data_pipeline.utility_code",
            "__main__"
        ],
        from_package="dags.data_pipeline"
    )

    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE
    )

    # DON'T DELETE. KEEP IT FOR REFERENCE
    # keepass_manager = providers.Singleton(
    #     PyKeePass,
    #     filename=providers.Callable(lambda: os.getenv("AIRFLOW_HOME") + "/Database.kdbx"),
    #     keyfile=providers.Callable(lambda: os.getenv("AIRFLOW_HOME") + "/Database.key")
    # )
    # DON'T DELETE. KEEP IT FOR REFERENCE



class EntryDetailsContainer(containers.DeclarativeContainer):
    """Generic container for building service entry details from KeePass."""

    # Dependencies injected by parent container
    config = providers.Configuration()
    secrets_backend = providers.Singleton(
        PyKeePass,
        filename=config.keepass.db_name,
        keyfile=config.keepass.key_file,
    )
    # keepass_manager = providers.Dependency()
    # entry_title_config_key = providers.Dependency()  # e.g., config.KeePass.JIRA_ENTRY

    service_entry_details = providers.Singleton(
        build_entry_details,
        keepass_manager=secrets_backend,
        title=config.entry_title,
    )

def make_schema_factories(km_provider, schemas):
    """Helper to create schema providers without scoping issues."""
    return {
        f"{schema}_{mode}": providers.Factory(
            build_entry_details,
            keepass_manager=km_provider,
            title=f"{schema}_{mode}"
        )
        for schema in schemas
        for mode in ("rw", "ro")
    }

class SchemaCredentialsDeclarativeContainer(containers.DeclarativeContainer):
    config = providers.Configuration()
    keepass_manager = providers.Dependency()

    jira = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.entries.jira
    )


    schema_providers = providers.Dict(
        **make_schema_factories(keepass_manager, ["public", "plat"])
    )



class SchemaCredentialsContainer(containers.DynamicContainer):
    def __init__(self, config, keepass_manager):
        super().__init__()
        self.keepass_manager = keepass_manager
        self.config = config

        print(f"type inside SchemaCredentialsContainer = {type(config)}")
        print(f"config = {config} and {self.config}")

        # Jira credentials
        self.jira = providers.Factory(
            build_entry_details,
            keepass_manager=self.keepass_manager,
            title=config.entries.jira
        )

        # Auto-generate schema RW/RO credentials

        for schema in self.config.schemas():
            for mode in ("rw", "ro"):
                # provider_name = f"{schema}_{mode}"
                # self.add_provider(
                #     provider_name,
                #     providers.Factory(
                #         build_entry_details,
                #         keepass_manager=self.keepass_manager,
                #         title=f"{schema}_{mode}"
                #     )
                # )
                setattr(
                    self,
                    f"{schema}_{mode}",
                    providers.Factory(
                        build_entry_details,
                        keepass_manager=self.keepass_manager,
                        title=f"{schema}_{mode}"
                    )
                )

class JiraEntryDetailsContainer(containers.DeclarativeContainer):
    """
    Specialized container for JIRA credential management.

    This container implements the Flowers refactoring pattern by:
    - Separating JIRA-specific credential concerns
    - Providing reusable JIRA entry details
    - Using consistent configuration management
    - Enabling dependency injection for KeePass manager

    Attributes:
        config: Configuration provider for JIRA settings
        keepass: Provider for KeePass manager (to be injected)
        jira_entry_details: Singleton factory for JIRA credentials
    """

    # Centralized configuration management
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # KeePass provider - to be overridden by parent container
    keepass = providers.Provider()

    # JIRA entry details with proper configuration
    jira_entry_details = providers.Singleton(
        build_entry_details,
        keepass_manager=keepass,
        title=config.KeePass.JIRA_ENTRY,
    )

    # Improved wiring configuration
    wiring_config = containers.WiringConfiguration([
        "__main__", __name__,
        # "dags.data_pipeline.utility_code"
    ])


class CircuitBreakerContainer(containers.DeclarativeContainer):
    logger_container = providers.Container(LoggerContainer)
    simplified_circuit_breaker = providers.Singleton(
        SimplifiedCircuitBreaker,
        logger=logger_container.logger
    )
    # global_circuit_breaker = providers.Singleton(GlobalCircuitBreaker)


class QueueContainer(containers.DeclarativeContainer):
    config = providers.Configuration()

    queues = providers.Dict(
        queue_issues=providers.Factory(asyncio.PriorityQueue, maxsize=1000),
        queue_stats=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_issue=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_changelog=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_worklog=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_comment=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_issue_links=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_issue_staging=providers.Factory(asyncio.PriorityQueue, maxsize=500),
        queue_upsert_issue=providers.Factory(asyncio.PriorityQueue),
    )

    queue_selector = providers.Selector(
        config.schema_name,
        acq=providers.Singleton(queues),
        plp=providers.Singleton(queues),
        plat=providers.Singleton(queues),
        train=providers.Singleton(queues),
        cpp=providers.Singleton(queues),
        ccp=providers.Singleton(queues),
    )
    # wiring_config = containers.WiringConfiguration(
    #     [
    #         "__main__",
    #         "dags.data_pipeline.utility_code"
    #     ]
    # )


# class ApplicationContainer(containers.DeclarativeContainer):
#     """
#     Main application container that provides all necessary dependencies.
#
#     This container implements the Flowers refactoring pattern by:
#     - Composing multiple specialized containers (Logger, KeePass, Database)
#     - Providing centralized dependency management
#     - Enabling configuration override and environment switching
#     - Managing application lifecycle through dependency injection
#     - Separating concerns across different container types
#
#     This container serves as the central dependency injection hub for the application,
#     providing access to:
#     - Logging services through LoggerContainer composition
#     - KeePass manager for credential management
#     - c session managers (both managed and unmanaged)
#     - Configuration management
#     - Lifecycle management for proper cleanup
#     - Circuit breaker for resilience patterns
#
#     The container supports dynamic schema switching and provides both Factory
#     and Singleton providers for different use cases.
#
#     Attributes:
#         logger_container: Composed LoggerContainer for logging services
#         config: Configuration loaded from YAML file
#         schema: Current database schema (default: "public")
#         keepass_manager: Singleton KeePass manager for credential access
#         pg_rw_entry: Factory for read-write database entry details
#         pg_ro_entry: Factory for read-only database entry details
#         database_rw: Factory for unmanaged read-write session manager
#         database_rw_managed: Singleton for managed read-write session manager
#         database_ro: Factory for unmanaged read-only session manager
#         database_ro_managed: Singleton for managed read-only session manager
#         lifecycle_manager: Global database lifecycle manager
#
#     Example:
#         >>> container = ApplicationContainer()
#         >>> container.wire(modules=[__name__])
#         >>>
#         >>> # Get managed database connection
#         >>> db_rw = container.database_rw_managed()
#         >>> with db_rw.session() as session:
#         ...     # Database operations
#         ...     pass
#         >>>
#         >>> # Schema switching
#         >>> container.schema.override("plat")
#         >>> db_plat = container.database_rw()
#     """
#
#     # Container composition - centralized logging
#     logger_container = providers.Container(LoggerContainer)
#
#     # Centralized configuration management
#     config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
#     config = providers.Configuration(yaml_files=[config_path])
#
#     schema = providers.Object("public")  # Default schema
#     # KeePass Manager
#     keepass_manager = providers.Singleton(
#         PyKeePass,
#         filename=config.KeePassDir.DB_NAME,
#         keyfile=config.KeePassDir.KEY_FILE
#     )
#     # Entry Providers
#     pg_rw_entry = providers.Factory(
#         build_entry_details,
#         keepass_manager=keepass_manager,
#         title=providers.Callable(lambda schema_name: f"{schema_name}_rw", schema_name=schema)
#     )
#
#     pg_ro_entry = providers.Factory(
#         build_entry_details,
#         keepass_manager=keepass_manager,
#         title=providers.Callable(lambda schema_name: f"{schema_name}_ro", schema_name=schema)
#     )
#
#     # Database Session Managers with proper dependency injection
#     database_rw = providers.Factory(
#         PostgresSessionManager,
#         entry=pg_rw_entry,
#         schema=schema,
#         rw=True,
#         logger=logger_container.logger
#     )
#
#     database_rw_managed = providers.Singleton(
#         ManagedPostgresSessionManager,
#         entry=pg_rw_entry,
#         schema=schema,
#         rw=True,
#         logger=logger_container.logger
#     )
#
#     database_rw_enhanced = providers.Factory(
#         EnhancedPostgresSessionManager,
#         entry=pg_rw_entry,
#         schema=schema,
#         rw=True,
#         logger=logger_container.logger
#     )
#
#     database_ro = providers.Factory(
#         PostgresSessionManager,
#         entry=pg_ro_entry,
#         schema=schema,
#         rw=False,
#         logger=logger_container.logger
#     )
#
#     database_ro_managed = providers.Singleton(
#         ManagedPostgresSessionManager,  # Fixed: should be ManagedPostgresSessionManager
#         entry=pg_ro_entry,
#         schema=schema,
#         rw=False,
#         logger=logger_container.logger
#     )
#
#     database_ro_enhanced = providers.Factory(
#         EnhancedPostgresSessionManager,
#         entry=pg_ro_entry,
#         schema=schema,
#         rw=False,
#         logger=logger_container.logger
#     )
#
#     # Lifecycle manager
#     lifecycle_manager = providers.Object(db_lifecycle_manager)
#
#     # Circuit breaker with proper logger injection
#     circuit_breaker = providers.Singleton(
#         SimplifiedCircuitBreaker,
#         logger=logger_container.logger,
#     )


class EnhancedApplicationContainer(containers.DeclarativeContainer):
    """
    Enhanced application container with improved separation of concerns.

    This container provides a cleaner, more modular approach to dependency
    injection with better testability and configuration management.
    """
    config_app = providers.Configuration(yaml_files=["./dags/data_pipeline/config.yaml"])

    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])



    # Configuration management
    # schema = providers.Configuration()
    schema = providers.Object("public")

    # External dependencies
    # logger_container = providers.DependenciesContainer()
    # keepass_container = providers.DependenciesContainer()
    logger_container = providers.Container(LoggerContainer)
    keepass_container = providers.Container(KeePassContainer)

    schema_credentials = providers.Singleton(
        SchemaCredentialsContainer,
        config=providers.Object(config_app),
        keepass_manager=keepass_container.keepass_manager
    )

    # Jira credentials
    # jira = providers.Container(
    #     EntryDetailsContainer,
    #     config=keepass_container.config,
    #     keepass_manager=keepass_container.keepass_manager,
    #     entry_title_config_key=config.KeePass.JIRA_ENTRY
    # )
    #
    # pg_rw = providers.Container(
    #     EntryDetailsContainer,
    #     config=keepass_container.config,
    #     keepass_manager=keepass_container.keepass_manager,
    #     title=config.KeePass.PG_RW,
    # )
    #
    # pg_ro = providers.Container(
    #     EntryDetailsContainer,
    #     config=keepass_container.config,
    #     keepass_manager=keepass_container.keepass_manager,
    #     title=config.KeePass.PG_RO,
    # )

    #
    # pg_ro = providers.Factory(
    #     build_entry_details,
    #     keepass_manager=keepass_manager,
    #     title=config.KeePass.PG_RO,
    # )

    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE
    )
    # Entry Providers
    pg_ro_entry = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=providers.Callable(lambda schema_name: f"{schema_name}_ro", schema_name=schema)
    )

    pg_rw_entry = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=providers.Callable(lambda schema_name: f"{schema_name}_rw", schema_name=schema)
    )

    # Core session managers with direct dependency injection
    base_session_manager_rw = providers.Factory(
        RefactoredPostgresSessionManager,
        entry=pg_rw_entry,
        schema=schema,
        rw=True,
        logger=logger_container.logger,
        create_async=False,
    )

    base_session_manager_ro = providers.Factory(
        RefactoredPostgresSessionManager,
        entry=pg_ro_entry,
        schema=schema,
        rw=True,
        logger=logger_container.logger,
        create_async=False,
    )




    # Enhanced session managers
    # enhanced_session_manager_rw = providers.Factory(
    #     EnhancedSessionManager,
    #     entry=pg_rw_entry,
    #     schema=schema,
    #     rw=True,
    #     logger=logger_container.logger
    # )

    # Task coordination components
    task_coordinator = providers.Callable(
        _get_task_lifecycle_coordinator,
        logger=logger_container.logger
    )

    shutdown_handler = providers.Singleton(
        ApplicationShutdownHandler
    )
    circuit_breaker = providers.Singleton(
        SimplifiedCircuitBreaker,
        logger=logger_container.logger,
    )

    # Convenience aliases for easy access
    # database_rw = base_session_manager_rw
    # database_ro = base_session_manager_ro
    # database_rw_enhanced = enhanced_session_manager_rw
    # database_ro_enhanced = enhanced_session_manager_ro



@inject
def get_kp_entry_details(
        title: str | None = None,
        ref: PyKeePass = Provide[KeePassContainer.keepass_manager]
) -> EntryDetails:
    try:
        entry = ref.find_entries(title=title, first=True)
        if not entry:
            raise LookupError(f"No entry found with given title")

        builder = EntryDetailsBuilder()
        builder.set_username(entry.username).set_password(entry.password).set_url(entry.url)
        for custom_property in entry.custom_properties:
            builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))

        return builder.build()

    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")



async def fetch_data(url: str):
    async with aiohttp.ClientSession().get(url) as response:
        print(response.status)
        return await response.json()  # or `await response.json()` if expecting JSON


async def send_data(url: str, data: dict):
    async with aiohttp.ClientSession().put(url, json=data) as response:
        return await response.json()


async def main():
    url = "https://example.com/api"
    data = {"key": "value"}

    # Use `http_session_provider` as an async context manager
    async with aiohttp.ClientSession() as http_session:
        # fetched_data = await fetch_data(url=url, http_session=http_session)
        # print(f"Fetched Data: {fetched_data}")

        # response_data = await send_data(url=url, data=data, http_session=http_session)
        # print(f"Response Data: {response_data}")
        response_fields = await fetch_data(url="https://corecard.atlassian.net/rest/api/3/field")
        print(json.dumps(response_fields))


def prepare_issue_classification_data(project_key: str, pg_session) -> pd.DataFrame:
    topq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                            literal(1).label('level'),
                            case(

                                (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                (Issue.issuetype == 'Epic', 'epic'),
                                (Issue.isSubTask.is_(True), 'subtask'),

                                else_="standard"
                            ).label("issueclass"),
                            cast(cast(Issue.id, TEXT), LtreeType).label("path_id"),
                            cast(func.replace(Issue.key, "-", "_"), LtreeType).label("path_key")
                            )
    topq = topq.filter(Issue.parent_key.is_(None))
    topq = topq.cte('cte', recursive=True)

    bottomq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                               topq.c.level + 1,
                               case(
                                   (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                   (Issue.issuetype == 'Epic', 'epic'),
                                   (Issue.isSubTask.is_(True), 'subtask'),
                                   else_="standard"
                               ).label("issueclass"),
                               topq.c.path_id.op('||')(func.text2ltree(cast(Issue.id, TEXT))),
                               topq.c.path_key.op('||')(func.text2ltree(func.replace(Issue.key, "-", "_")))
                               )
    bottomq = bottomq.join(topq, Issue.parent_key == topq.c.key)
    recursive_q = topq.union_all(bottomq)
    res = pg_session.query(recursive_q).all()
    df = pd.DataFrame(res)
    # df = pd.read_sql(session.query(recursive_q).statement, session.bind)
    return df


# Usage example with proper cleanup
async def main_with_cleanup():
    """Example of proper usage with cleanup."""
    try:
        # Your application logic here
        keepass_container = KeePassContainer()
        await keepass_container.init_resources()

        # database_container = CoreSessionManagerContainer()
        # database_container.pg_rw_entry.override(keepass_container.pg_rw)
        # database_container.pg_ro_entry.override(keepass_container.pg_ro)
        # await database_container.init_resources()

        # Use the database instances
        # db_rw = database_container.database_rw()

        # async with db_rw.async_session() as session:
        #     pass

    except asyncio.CancelledError:
        print("Main application was cancelled")
        raise
    except Exception as e:
        print(f"Error in main application: {e}")
        raise



# Example 1: Sync usage with automatic cleanup
@inject
def sync_database_operation(
        db_rw = Provide[EnhancedApplicationContainer.base_session_manager_rw]
):
    """Example sync function with database operations."""
    # with database_container.database_rw().update_schema('plat').session() as pg_session:
    print(f"sync_database_operation called")
    with db_rw.session() as session:
        print(f"database url plat: {session.bind.engine}")
        df = prepare_issue_classification_data('plat', session)
        print(f"Total rows: {df.shape[0]}")
    # Cleanup will happen automatically via atexit/signal handlers


# Example 2: Async usage with context manager
async def async_database_operation():
    """Example async function with proper cleanup."""
    container = EnhancedApplicationContainer()
    container.wire(modules=[__name__])

    # async with database_lifecycle():
    #     db_rw = container.database_rw()
    #     async with db_rw.async_session() as session:
    #         print(session.bind.engine)

        # Cleanup happens automatically when exiting context


# Example 3: Manual cleanup control
# @inject
# async def manual_cleanup_example(
#         lifecycle_manager = Provide[EnhancedApplicationContainer.lifecycle_manager]
# ):
#     """Example with manual cleanup control."""
#     try:
#         # Your application logic
#         pass
#     finally:
#         # Manual cleanup when needed
#         await lifecycle_manager.async_cleanup()


# Example 4: Decorator-based cleanup

@asynccontextmanager
async def database_cleanup_context():
    """Context manager ensuring proper database cleanup order."""
    try:
        yield
    finally:
        # Ensure database cleanup happens before event loop closure
        # await RefactoredPostgresSessionManager.cleanup_all_instances()
        # await db_lifecycle_manager.async_cleanup()
        pass

if __name__ == "__main__":
    """
    Usage examples demonstrating different database operations across schemas.

    This section provides comprehensive examples for:
    - Working with different database schemas (public, plat, plp)
    - Both synchronous and asynchronous database operations
    - Proper cleanup and lifecycle management
    - Container configuration and dependency injection
    """
    with open("dags/data_pipeline/config.yaml", 'r') as f:
        config_data = yaml.safe_load(f)

    print(config_data)

    keepass_container = KeePassContainer()
    keepass_container.wire(modules=[__name__])

    print(keepass_container.keepass_manager())
    db_container = DatabaseContainer()
    db_container.config.from_dict(config_data)
    db_container.wire(modules=[__name__])
    # schemas = db_container.config.schemas.provided()
    # sync_managers = {}
    # for schema in schemas:
    #     sync_managers[schema] = providers.Singleton(
    #         PostgresSessionManager.create_sync_only,
    #         entry=providers.Factory(
    #             build_entry_details,
    #             keepass_manager=db_container.keepass_manager,
    #             title=f"{schema}_rw"
    #         ),
    #         schema=schema,
    #         rw=True,
    #         logger=providers.Factory(logging.getLogger, f"db.sync.{schema}")
    #     )
    #
    # # Add the sync_session_managers to the container
    # db_container.sync_session_managers = providers.Dict(**sync_managers)

    print(db_container.sync_session_managers()["plat"])

    exit(0)

    service_container = EntryDetailsContainer()
    app = EnhancedApplicationContainer()
    app.wire(modules=[__name__])
    app.init_resources()
    print(app.config())
    print(app.config_app())

    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    hardcoded_path = "./logging_config.yaml"

    print(f"config_path resolves to: {os.path.abspath(config_path)}")
    print(f"hardcoded_path resolves to: {os.path.abspath(hardcoded_path)}")
    print(f"config_path exists: {os.path.exists(config_path)}")
    print(f"hardcoded_path exists: {os.path.exists(hardcoded_path)}")

    app_container = SchemaCredentialsDeclarativeContainer()
    app_container.wire(modules=[__name__])
    app_container.keepass_manager.override(keepass_container.keepass_manager)
    app_container.config.from_dict({
        "entries": {"jira": "corecard Jira"},
        "schemas": ["public", "plat"]
    })
    print(app_container.schema_providers())


    # jira_container = app.jira()

    # entry_details = app.jira().service_entry_details()
    # print(entry_details)

    print("Showing Dynamic container")
    print(type(app.schema_credentials))
    jira_entry = app.schema_credentials().jira()
    print(jira_entry)




    def example_public_schema():
        """Example: Working with public schema."""
        print("\n=== PUBLIC SCHEMA EXAMPLE ===")

        # Initialize containers
        keepass_container = KeePassContainer()
        # database_container = CoreSessionManagerContainer()
        #
        # # Configure database container with KeePass credentials
        # database_container.pg_rw_entry.override(keepass_container.pg_rw)
        # database_container.pg_ro_entry.override(keepass_container.pg_ro)
        # database_container.schema.override('public')
        #
        # # Get managed database connection
        # db_rw = database_container.database_rw()
        #
        # try:
        #     with db_rw.session() as session:
        #         print(f"Connected to public schema: {session.bind.engine}")
        #         # Example query
        #         stmt = select(User.displayName).limit(5)
        #         result = session.execute(stmt).all()
        #         print(f"Sample users: {result}")
        # except Exception as e:
        #     print(f"Error in public schema example: {e}")

    def example_plat_schema():
        """Example: Working with plat schema."""
        print("\n=== PLAT SCHEMA EXAMPLE ===")

        # Initialize containers
        keepass_container = KeePassContainer()
        # database_container = CoreSessionManagerContainer()

        # Configure for plat schema
        keepass_container.rw_title.override('plat_rw')
        keepass_container.ro_title.override('plat_ro')
        # database_container.pg_rw_entry.override(keepass_container.schema_rw)
        # database_container.pg_ro_entry.override(keepass_container.schema_ro)
        # database_container.schema.override('plat')
        #
        # # Get database connection
        # db_rw = database_container.database_rw()

        # try:
        #     with db_rw.session() as session:
        #         print(f"Connected to plat schema: {session.bind.engine}")
        #         # Example: Get issue classification data
        #         df = prepare_issue_classification_data('plat', session)
        #         print(f"PLAT schema - Total issue rows: {df.shape[0]}")
        # except Exception as e:
        #     print(f"Error in plat schema example: {e}")

    def example_plp_schema():
        """Example: Working with plp schema."""
        print("\n=== PLP SCHEMA EXAMPLE ===")

        # Initialize containers
        keepass_container = KeePassContainer()
        # database_container = DatabaseSessionManagerContainer()
        # database_container = CoreSessionManagerContainer()

        # Configure for plp schema
        keepass_container.rw_title.override('plp_rw')
        keepass_container.ro_title.override('plp_ro')
        # database_container.pg_rw_entry.override(keepass_container.schema_rw)
        # database_container.pg_ro_entry.override(keepass_container.schema_ro)
        # database_container.schema.override('plp')
        #
        # # Get database connection
        # db_rw = database_container.database_rw()
        #
        # try:
        #     with db_rw.session() as session:
        #         print(f"Connected to plp schema: {session.bind.engine}")
        #         # Example: Get issue classification data
        #         df = prepare_issue_classification_data('plp', session)
        #         print(f"PLP schema - Total issue rows: {df.shape[0]}")
        # except Exception as e:
        #     print(f"Error in plp schema example: {e}")

    async def example_async_operations():
        """Example: Asynchronous database operations with proper cleanup."""
        print("\n=== ASYNC OPERATIONS EXAMPLE ===")

        # async with database_lifecycle():
        #     # Initialize containers
        #     keepass_container = KeePassContainer()
        #     # database_container = DatabaseSessionManagerContainer()
        #     database_container = CoreSessionManagerContainer()
        #
        #     # Configure database container
        #     database_container.pg_rw_entry.override(keepass_container.pg_rw)
        #     database_container.pg_ro_entry.override(keepass_container.pg_ro)
        #
        #     # Get managed database connection
        #     db_rw = database_container.database_rw()
        #
        #     try:
        #         async with db_rw.async_session() as session:
        #             print(f"Async connection: {session.bind.engine}")
        #             # Example async query
        #             stmt = select(User.displayName).limit(3)
        #             result = await session.execute(stmt)
        #             users = result.all()
        #             print(f"Async query result: {users}")
        #
        #         # Test schema switching
        #         db_rw.update_schema('plat')
        #         async with db_rw.async_session() as session:
        #             print(f"Switched to plat schema: {session.bind.engine}")
        #
        #     except Exception as e:
        #         print(f"Error in async operations: {e}")

    def example_application_container():
        """Example: Using ApplicationContainer for dependency injection."""
        print("\n=== APPLICATION CONTAINER EXAMPLE ===")

        # Initialize and wire the application container
        container = EnhancedApplicationContainer()
        container.wire(modules=[__name__])

        try:
            # Use injected dependencies
            sync_database_operation()

            # Manual container usage
            db_managed = container.database_rw_managed()
            with db_managed.session() as session:
                print(f"Managed connection: {session.bind.engine}")

        except Exception as e:
            print(f"Error in application container example: {e}")

    def example_jira_credentials():
        """Example: Accessing Jira credentials from KeePass."""
        print("\n=== JIRA CREDENTIALS EXAMPLE ===")

        try:
            # Get Jira credentials
            cc_jira = get_kp_entry_details("corecard Jira")
            auth_token = f'{cc_jira.username}:{cc_jira.password}'
            encoded_token = base64.b64encode(auth_token.encode()).decode()
            print(f"Jira auth token (base64): {encoded_token[:20]}...")

        except Exception as e:
            print(f"Error accessing Jira credentials: {e}")

    # Run all examples
    print("Running database container usage examples...")

    try:
        # Synchronous examples
        example_public_schema()
        example_plat_schema()
        example_plp_schema()
        example_application_container()
        example_jira_credentials()

        # Asynchronous example
        print("\nRunning async example...")
        asyncio.run(example_async_operations())

    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nError running examples: {e}")

    print("\nAll examples completed. Database connections will be cleaned up automatically.")



